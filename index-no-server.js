const puppeteer = require('puppeteer');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const puppeteerExtra = require('puppeteer-extra');
const fs = require('fs');
const path = require('path');
const CaptchaHandler = require('./captcha.js');
const EmailHandler = require('./email.js');

puppeteerExtra.use(StealthPlugin());

class AutoRegisterNoServer {
    constructor(options = {}) {
        this.browser = null;
        this.page = null;
        this.authToken = options.authToken || null;
        this.authUrl = options.authUrl || null;
        this.directUrl = options.directUrl || null; // 直接注册URL
        this.tempEmail = null;
        this.stepCounter = 0;

        this.captchaHandler = new CaptchaHandler();
        this.emailHandler = new EmailHandler();

        this.imageDir = path.join(__dirname, 'image');
        if (!fs.existsSync(this.imageDir)) {
            fs.mkdirSync(this.imageDir, { recursive: true });
        }
    }

    log(message) {
        console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async takeScreenshot(step) {
        try {
            this.stepCounter++;
            const filename = `STEP_${this.stepCounter.toString().padStart(2, '0')}_${step}.png`;
            const filepath = path.join(this.imageDir, filename);
            await this.page.screenshot({ path: filepath, fullPage: true });
            this.log(`截图保存: ${filename}`);
        } catch (error) {
            this.log(`截图失败: ${error.message}`);
        }
    }

    async initBrowser() {
        this.log('启动浏览器 (无代理模式)');

        const launchOptions = {
            headless: false, // 改为可见模式，方便调试
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--ignore-certificate-errors',
                '--disable-blink-features=AutomationControlled'
            ],
            defaultViewport: { width: 1366, height: 768 },
            timeout: 120000
        };

        this.browser = await puppeteerExtra.launch(launchOptions);
        this.page = await this.browser.newPage();
        this.page.setDefaultTimeout(120000);
        this.page.setDefaultNavigationTimeout(120000);

        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            window.chrome = { runtime: {} };
            Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });
            Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
        });
    }

    async handleCaptchaIfPresent() {
        try {
            this.log('检查验证码...');
            const captchaResult = await this.captchaHandler.solveCaptcha(this.page);
            if (captchaResult) {
                this.log('验证码处理完成');
                await this.takeScreenshot('captcha_solved');
            } else {
                this.log('未发现验证码或验证码处理失败');
            }
        } catch (error) {
            this.log(`验证码处理错误: ${error.message}`);
        }
    }

    async register() {
        try {
            await this.initBrowser();
            await this.takeScreenshot('browser_started');

            // 生成临时邮箱
            this.tempEmail = await this.emailHandler.generateTempEmail();
            this.log(`邮箱: ${this.tempEmail}`);

            let targetUrl;
            
            if (this.directUrl) {
                // 方案1: 直接访问注册URL
                targetUrl = this.directUrl;
                this.log(`直接访问注册页面: ${targetUrl}`);
            } else if (this.authUrl) {
                // 方案2: 使用提供的授权URL
                targetUrl = this.authUrl;
                this.log(`使用提供的授权URL: ${targetUrl}`);
            } else {
                throw new Error('请提供 directUrl 或 authUrl 参数');
            }

            await this.page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 120000 });
            await this.wait(5000);
            await this.takeScreenshot('page_loaded');

            await this.handleCaptchaIfPresent();

            // 填写注册表单
            await this.fillRegistrationForm();
            await this.takeScreenshot('form_filled');

            // 提交表单
            await this.submitForm();
            await this.takeScreenshot('form_submitted');

            // 等待并处理结果
            await this.wait(5000);
            const result = await this.getRegistrationResult();
            
            this.log('注册流程完成');
            return result;

        } catch (error) {
            this.log(`注册过程出错: ${error.message}`);
            await this.takeScreenshot('error');
            throw error;
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }

    async fillRegistrationForm() {
        try {
            this.log('填写注册表单...');

            // 查找邮箱输入框
            const emailSelectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="邮箱" i]',
                'input[placeholder*="email" i]',
                'input[name*="mail"]'
            ];

            let emailInput = null;
            for (const selector of emailSelectors) {
                try {
                    emailInput = await this.page.$(selector);
                    if (emailInput) break;
                } catch (e) {}
            }

            if (emailInput) {
                await emailInput.click();
                await emailInput.clear();
                await emailInput.type(this.tempEmail, { delay: 100 });
                this.log(`邮箱已填写: ${this.tempEmail}`);
            } else {
                this.log('未找到邮箱输入框');
            }

            // 查找其他可能的输入框并填写
            await this.fillOtherFields();

        } catch (error) {
            this.log(`填写表单失败: ${error.message}`);
            throw error;
        }
    }

    async fillOtherFields() {
        // 查找用户名输入框
        const usernameSelectors = [
            'input[name="username"]',
            'input[name="user"]',
            'input[placeholder*="用户名" i]',
            'input[placeholder*="username" i]'
        ];

        for (const selector of usernameSelectors) {
            try {
                const input = await this.page.$(selector);
                if (input) {
                    const randomUsername = 'user' + Math.random().toString(36).substr(2, 8);
                    await input.click();
                    await input.type(randomUsername, { delay: 100 });
                    this.log(`用户名已填写: ${randomUsername}`);
                    break;
                }
            } catch (e) {}
        }

        // 查找密码输入框
        const passwordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[placeholder*="密码" i]',
            'input[placeholder*="password" i]'
        ];

        for (const selector of passwordSelectors) {
            try {
                const input = await this.page.$(selector);
                if (input) {
                    const randomPassword = 'Pass' + Math.random().toString(36).substr(2, 8) + '!';
                    await input.click();
                    await input.type(randomPassword, { delay: 100 });
                    this.log(`密码已填写: ${randomPassword}`);
                    break;
                }
            } catch (e) {}
        }
    }

    async submitForm() {
        try {
            this.log('提交注册表单...');

            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("注册")',
                'button:contains("Register")',
                'button:contains("Sign Up")',
                '.submit-btn',
                '.register-btn'
            ];

            let submitted = false;
            for (const selector of submitSelectors) {
                try {
                    const button = await this.page.$(selector);
                    if (button) {
                        await button.click();
                        submitted = true;
                        this.log('表单已提交');
                        break;
                    }
                } catch (e) {}
            }

            if (!submitted) {
                // 尝试按回车键
                await this.page.keyboard.press('Enter');
                this.log('尝试按回车键提交');
            }

        } catch (error) {
            this.log(`提交表单失败: ${error.message}`);
            throw error;
        }
    }

    async getRegistrationResult() {
        try {
            // 等待页面响应
            await this.wait(3000);

            // 检查是否有成功消息
            const successSelectors = [
                '.success',
                '.success-message',
                '[class*="success"]',
                '[id*="success"]'
            ];

            for (const selector of successSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        const text = await this.page.evaluate(el => el.textContent, element);
                        this.log(`成功消息: ${text}`);
                        return { success: true, message: text, email: this.tempEmail };
                    }
                } catch (e) {}
            }

            // 检查当前URL和页面内容
            const currentUrl = this.page.url();
            const pageTitle = await this.page.title();
            
            this.log(`当前URL: ${currentUrl}`);
            this.log(`页面标题: ${pageTitle}`);

            // 尝试获取页面上的token或重要信息
            const tokens = await this.extractTokensFromPage();
            
            return {
                success: true,
                url: currentUrl,
                title: pageTitle,
                email: this.tempEmail,
                tokens: tokens
            };

        } catch (error) {
            this.log(`获取注册结果失败: ${error.message}`);
            return { success: false, error: error.message, email: this.tempEmail };
        }
    }

    async extractTokensFromPage() {
        try {
            // 尝试从页面中提取token
            const tokens = await this.page.evaluate(() => {
                const results = {};
                
                // 查找页面中的token
                const tokenElements = document.querySelectorAll('[data-token], [id*="token"], [class*="token"]');
                tokenElements.forEach((el, index) => {
                    results[`token_${index}`] = el.textContent || el.value || el.getAttribute('data-token');
                });

                // 查找localStorage中的token
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.toLowerCase().includes('token')) {
                        results[`localStorage_${key}`] = localStorage.getItem(key);
                    }
                }

                // 查找sessionStorage中的token
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && key.toLowerCase().includes('token')) {
                        results[`sessionStorage_${key}`] = sessionStorage.getItem(key);
                    }
                }

                return results;
            });

            if (Object.keys(tokens).length > 0) {
                this.log('找到的tokens:');
                Object.entries(tokens).forEach(([key, value]) => {
                    this.log(`  ${key}: ${value}`);
                });
            }

            return tokens;
        } catch (error) {
            this.log(`提取token失败: ${error.message}`);
            return {};
        }
    }
}

// 使用示例
async function main() {
    // 方案1: 直接访问注册页面
    const autoRegister1 = new AutoRegisterNoServer({
        directUrl: 'https://example.com/register' // 替换为实际的注册URL
    });

    // 方案2: 使用已知的授权URL
    const autoRegister2 = new AutoRegisterNoServer({
        authUrl: 'https://example.com/oauth/authorize?client_id=xxx&redirect_uri=xxx' // 替换为实际的授权URL
    });

    // 方案3: 使用已知的auth token和授权URL
    const autoRegister3 = new AutoRegisterNoServer({
        authToken: 'your-auth-token-here', // 如果你有现成的token
        authUrl: 'https://example.com/oauth/authorize?client_id=xxx&redirect_uri=xxx'
    });

    try {
        console.log('🚀 开始注册 (无需3000端口)');
        
        // 选择其中一个方案运行
        const result = await autoRegister1.register(); // 或 autoRegister2.register() 或 autoRegister3.register()
        
        console.log('🎉 注册成功:', result);
        
        // 打印找到的tokens
        if (result.tokens && Object.keys(result.tokens).length > 0) {
            console.log('\n📋 找到的Tokens:');
            Object.entries(result.tokens).forEach(([key, value]) => {
                console.log(`${key}: ${value}`);
            });
        }

    } catch (error) {
        console.error('💥 注册失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = AutoRegisterNoServer;
